import type { User } from './authApi';
import { api } from './index.ts';

interface UpdateProfileRequest {
  name: string;
  surname: string;
  username: string;
  birthday: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ChangePasswordResponse {
  message: string;
}

interface NotificationSettings {
  emailMarketing: boolean;
  bonusNotifications: boolean;
  winNotifications: boolean;
  depositNotifications: boolean;
  withdrawalNotifications: boolean;
}

type UpdateNotificationSettingsRequest = NotificationSettings;

interface UpdateNotificationSettingsResponse {
  message: string;
  settings: NotificationSettings;
}

// Multiple Logins types
interface LoginSession {
  id: string;
  deviceInfo: string;
  ipAddress: string;
  status: 'active' | 'not_active';
  lastActive: string;
  location?: string;
  browser?: string;
  os?: string;
}

interface GetLoginSessionsResponse {
  sessions: LoginSession[];
}

interface TerminateSessionRequest {
  sessionId: string;
}

interface TerminateSessionResponse {
  message: string;
}

interface TerminateAllSessionsResponse {
  message: string;
  terminatedCount: number;
}

// User API slice - manages user profile, settings, and account operations
export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    updateProfile: builder.mutation<User, UpdateProfileRequest>({
      query: (data) => ({
        url: '/auth/profile',
        method: 'PUT',
        body: data,
      }),
    }),

    changePassword: builder.mutation<ChangePasswordResponse, ChangePasswordRequest>({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    getNotificationSettings: builder.query<NotificationSettings, void>({
      query: () => ({
        url: '/user/notification-settings',
        method: 'GET',
      }),
      providesTags: ['NotificationSettings'],
    }),

    updateNotificationSettings: builder.mutation<
      UpdateNotificationSettingsResponse,
      UpdateNotificationSettingsRequest
    >({
      query: (data) => ({
        url: '/user/notification-settings',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['NotificationSettings'],
    }),

    // Multiple Logins endpoints
    getLoginSessions: builder.query<GetLoginSessionsResponse, void>({
      query: () => ({
        url: '/user/login-sessions',
        method: 'GET',
      }),
      providesTags: ['LoginSessions'],
    }),

    terminateSession: builder.mutation<TerminateSessionResponse, TerminateSessionRequest>({
      query: (data) => ({
        url: `/user/login-sessions/${data.sessionId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['LoginSessions'],
    }),

    terminateAllSessions: builder.mutation<TerminateAllSessionsResponse, void>({
      query: () => ({
        url: '/user/login-sessions/terminate-all',
        method: 'DELETE',
      }),
      invalidatesTags: ['LoginSessions'],
    }),
  }),
});

export const {
  useUpdateProfileMutation,
  useChangePasswordMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  useGetLoginSessionsQuery,
  useTerminateSessionMutation,
  useTerminateAllSessionsMutation,
} = userApi;

export type { NotificationSettings, LoginSession };
