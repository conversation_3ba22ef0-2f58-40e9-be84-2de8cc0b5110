import {
  type BaseQueryFn,
  createApi,
  type FetchArgs,
  fetchBaseQuery,
  type FetchBaseQueryError,
} from '@reduxjs/toolkit/query/react';
import { AppLocalStorage } from '@/utils';
import { ACCESS_TOKEN_NAME } from '@/utils/constants.ts';

const BASE_URL = '';
const PRIVATE_LOCAL_API_BASE_URL = `${BASE_URL}/api`;

const baseQuery = fetchBaseQuery({
  baseUrl: PRIVATE_LOCAL_API_BASE_URL,
  prepareHeaders: (headers) => {
    const accessToken = AppLocalStorage.getItem({ key: ACCESS_TOKEN_NAME });
    if (accessToken) headers.set('Authorization', `Bearer ${accessToken}`);
    return headers;
  },
});

const baseQueryNoRefresh: BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> = async (
  args,
  api,
  extra,
) => {
  const res = await baseQuery(args, api, extra);

  // error interception
  if (res.error?.status === 401) {
    // AppLocalStorage.removeItem(ACCESS_TOKEN_NAME);
    // api.dispatch(authSlice.actions.loggedOut());
  }

  return res;
};

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryNoRefresh,
  endpoints: () => ({}),
  tagTypes: ['NotificationSettings', 'LoginSessions'],
});
