import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Extend dayjs with relativeTime plugin
dayjs.extend(relativeTime);

/**
 * Formats a date string to show relative time or absolute date
 * Format: "12:30 16.12.2024" for dates older than 24 hours
 * @param dateString - ISO date string
 * @returns Formatted time string
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = dayjs(dateString);
  const now = dayjs();
  const diffInHours = now.diff(date, 'hour');

  if (diffInHours < 1) {
    return 'Just now';
  }
  if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  }

  // Format as "12:30 16.12.2024"
  return date.format('HH:mm DD.MM.YYYY');
};

/**
 * Formats a date to a readable string
 * @param date - Date object, dayjs object, or ISO string
 * @param format - dayjs format string (default: 'DD.MM.YYYY')
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | string | dayjs.Dayjs,
  format: string = 'DD.MM.YYYY',
): string => {
  return dayjs(date).format(format);
};

/**
 * Formats a date to include time in format "12:30 16.12.2024"
 * @param date - Date object, dayjs object, or ISO string
 * @returns Formatted date and time string
 */
export const formatDateTime = (date: Date | string | dayjs.Dayjs): string => {
  return dayjs(date).format('HH:mm DD.MM.YYYY');
};
