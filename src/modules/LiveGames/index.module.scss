@use '@/styles/media' as *;

.wrapper {
  margin-top: 54px;

  @include mobile {
    margin-top: 48px;
  }
}

.swiperContainer {
  overflow-x: hidden;
  width: 100%;
}

.topHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  @include mobile {
    margin-bottom: 24px;
  }
}

.headerTopLeft {
  display: flex;
  column-gap: 12px;
  align-items: center;

  & .liveGamesIcon {
    fill: var(--neutral-300)
  }
}

.headerMiddleMobile {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 16px;

  & > div {
    flex: 1;
  }

  & > div:first-child {
    flex-grow: 0;
  }
}

.title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--white2);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  column-gap: 16px;

  .navButtons {
    display: flex;
    gap: 8px;
  }
}

.card {
  height: 40px;
  transition: 0.3s ease-in-out;
}

