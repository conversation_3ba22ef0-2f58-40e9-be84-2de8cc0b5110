import styles from './index.module.scss';

import mockBannerImage1 from '@/assets/mocks/mock-banner-1.png';
import mockBannerImage2 from '@/assets/mocks/mock-banner-2.png';
import { Swiper, SwiperSlide } from 'swiper/react';

import { Autoplay } from 'swiper/modules';

import 'swiper/css';
import 'swiper/css/autoplay';

import { useMemo, useRef, useState } from 'react';
import clsx from 'clsx';

import ChevronLeftIcon from '@/assets/icons/chevron-left.svg?react';
import ChevronRightIcon from '@/assets/icons/chevron-right.svg?react';

import ArrowLeftIcon from '@/assets/icons/arrow-left.svg?react';
import ArrowRightIcon from '@/assets/icons/arrow-right.svg?react';
import { useResponsive } from '@/hooks';

const slides = [
  {
    id: 1,
    title: 'Money back as cash\nif your first bet loses',
    cta: 'Create account',
    secondary: 'View offer',
    image: mockBannerImage1,
  },
  {
    id: 2,
    title: 'The thrill of racing\nstarts here',
    cta: 'Join the action',
    image: mockBannerImage2,
  },
  {
    id: 3,
    title: 'Live in-play betting\nnow available',
    cta: 'Bet live',
    image: mockBannerImage1,
  },
  {
    id: 4,
    title: 'Boost your odds\nwith our promo',
    cta: 'Check boost',
    image: mockBannerImage2,
  },
];

const BULLET_WIDTH = 35;
const ACTIVE_BULLET_WIDTH = 73;
const BULLET_GAP = 8;

export const PromoSlider = () => {
  //eslint-disable-next-line
  const swiperRef = useRef<any>(null);
  const { isTablet, isMobile, isDesktop } = useResponsive();
  const [activeIndex, setActiveIndex] = useState(0);

  const slidesPerGroup = isDesktop ? 2 : 1;

  const bulletCount = useMemo(() => {
    return isDesktop ? Math.ceil(slides.length / slidesPerGroup) : slides.length;
  }, [isDesktop, slidesPerGroup]);

  const activeBulletIndex = useMemo(() => {
    return isDesktop ? Math.floor(activeIndex / slidesPerGroup) : activeIndex;
  }, [activeIndex, isDesktop, slidesPerGroup]);

  const bulletContainerWidth = useMemo(() => {
    const passiveBullets = bulletCount - 1;
    const gaps = bulletCount - 1;
    return passiveBullets * BULLET_WIDTH + ACTIVE_BULLET_WIDTH + gaps * BULLET_GAP;
  }, [bulletCount]);

  const isTabletOrMobile = useMemo(() => isTablet || isMobile, [isMobile, isTablet]);

  return (
    <div className={styles.sliderWrapper}>
      <div className={styles.swiperContainer}>
        <Swiper
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          key={isDesktop ? 'desktop' : 'mobile'}
          onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
          slidesPerView={isDesktop ? 2 : 1}
          spaceBetween={isDesktop ? 16 : 12}
          slidesPerGroup={slidesPerGroup}
          pagination={{ clickable: true }}
          loop
          autoplay={{ delay: 3000, pauseOnMouseEnter: true }}
          breakpoints={{
            1920: { slidesPerView: 2, spaceBetween: 16 },
          }}
          modules={[Autoplay]}
        >
          {slides.map((s) => (
            <SwiperSlide key={s.id}>
              <div className={styles.slide} style={{ backgroundImage: `url(${s.image})` }}></div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      <div className={styles.controls}>
        <button className={styles.arrowButton} onClick={() => swiperRef.current?.slidePrev()}>
          {isTabletOrMobile ? <ArrowLeftIcon /> : <ChevronLeftIcon />}
        </button>

        <div className={styles.bullets} style={{ width: bulletContainerWidth }}>
          {Array.from({ length: bulletCount }).map((_, i) => (
            <div
              key={i}
              className={clsx(styles.bullet, {
                [styles.active]: i === activeBulletIndex,
              })}
              onClick={() => swiperRef.current?.slideToLoop(i * slidesPerGroup)}
            />
          ))}
        </div>

        <button className={styles.arrowButton} onClick={() => swiperRef.current?.slideNext()}>
          {isTabletOrMobile ? <ArrowRightIcon /> : <ChevronRightIcon />}
        </button>
      </div>
    </div>
  );
};
