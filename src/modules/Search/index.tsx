import { useMemo, useState } from 'react';
import styles from './index.module.scss';
import clsx from 'clsx';
import SearchIcon from '@/assets/icons/search.svg?react';
import CloseIcon from '@/assets/icons/close_24px.svg?react';
import NoResultsIcon from '@/assets/icons/search-no-result.svg?react';
import EarthIcon from '@/assets/icons/flat-color-icons_globe.svg?react';

const mockResults = [
  { id: '1', name: 'UEFA Champions League', category: 'International' },
  { id: '2', name: 'UEFA Europa League', category: 'International' },
  { id: '3', name: 'UEFA Europa Conference League', category: 'International' },
];

const highlightMatch = (text: string, query: string) => {
  if (!query) return text;

  const regex = new RegExp(`(${query})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part, i) =>
    regex.test(part) ? (
      <span key={i} className={styles.highlight}>
        {part}
      </span>
    ) : (
      <span key={i}>{part}</span>
    ),
  );
};

export const Search = () => {
  const [query, setQuery] = useState('');
  const [focused, setFocused] = useState(false);

  const filteredResults =
    query.length >= 3
      ? mockResults.filter((r) => r.name.toLowerCase().includes(query.toLowerCase()))
      : [];

  const inputActive = useMemo(() => focused || query, [focused, query]);

  const showEmptyState = useMemo(
    () => filteredResults.length === 0 && inputActive,
    [filteredResults.length, inputActive],
  );

  return (
    <div className={styles.searchWrapper}>
      <div className={clsx(styles.searchInputWrapper, inputActive && styles.active)}>
        <SearchIcon className={clsx(styles.searchIcon, inputActive && styles.active)} />
        <input
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          type="text"
          placeholder="Search"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className={styles.input}
        />
        {inputActive && (
          <button onClick={() => setQuery('')} className={styles.clearButton}>
            <CloseIcon />
          </button>
        )}
      </div>

      {showEmptyState ? (
        <div className={styles.emptyState}>
          <NoResultsIcon className={styles.emptyIcon} />
          <p className={styles.emptyTitle}>Search for sports, leagues, or events</p>
          <p className={styles.emptyHint}>Enter at least 3 letters to start search</p>
        </div>
      ) : (
        filteredResults.length > 0 && (
          <div className={styles.resultsList}>
            {filteredResults.map((item) => (
              <div key={item.id} className={styles.resultItem}>
                <EarthIcon />
                <div>
                  <div className={styles.resultMeta}>{item.category}</div>
                  <div className={styles.resultName}>{highlightMatch(item.name, query)}</div>
                </div>
              </div>
            ))}
          </div>
        )
      )}
    </div>
  );
};
