@use '@/styles/media' as *;

.searchWrapper {
  width: 100%;
  margin: 24px auto 0;
  color: var(--white);
  position: relative;
}

.searchInputWrapper {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  background-color: var(--neutral-600);
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 0 17px;
  height: 44px;
  transition: border-color 0.2s ease;

  &.active {
    border-color: var(--primary-500);
    background-color: var(--primary-900);
  }
}

.searchIcon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-right: 8px;
  fill: var(--neutral-400);
  transition: 0.2s ease;

  &.active {
    fill: var(--white);
  }
}

.cleanIcon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  transition: 0.2s ease;
}

.input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--white);
  font-weight: 400;
  font-family: 'Poppins', sans-serif;
  font-size: var(--font-size-sm-md);

  &::placeholder {
    color: var(--neutral-400);
    font-size: var(--font-size-sm-md);
    font-weight: var(--font-weight-normal);
  }
}

.clearButton {
  display: flex;
  background: none;
  border: none;
  margin-left: 8px;
  cursor: pointer;

  svg {
    width: 16px;
    height: 16px;
    fill: var(--neutral-400);
  }
}

.emptyState {
  position: absolute;
  width: 100%;
  background-color: var(--neutral-600);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: -12px;
  padding: 24px 0 12px;
  height: 280px;
  z-index: 2;

  @include tablet {
    width: calc(100% - 24px);
  }
}

.emptyIcon {
  width: 70px;
  height: 70px;
  margin-bottom: 24px;
}

.emptyTitle {
  margin-bottom: 8px;
  font-size: var(--font-size-sm-md);
  font-weight: var(--font-weight-semibold);
  color: var(--neutral-50);
}

.emptyHint {
  font-size: var(--font-size-xs);
  color: var(--neutral-50);
  opacity: 0.5;
}

.resultsList {
  position: absolute;
  width: 100%;
  background-color: var(--neutral-600);
  border-radius: 8px;
  overflow: hidden;
  margin-top: -12px;
  padding: 24px 12px 12px;
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  z-index: 2;

  @include tablet {
    width: calc(100% - 24px);
  }
}

.resultItem {
  display: flex;
  column-gap: 12px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 8px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--neutral-500);
  }
}

.resultMeta {
  font-size: var(--font-size-xss);
  color: var(--neutral-20);
  margin-bottom: 4px;
}

.resultName {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--white);
}

.highlight {
  color: var(--primary-500);
}

@include tablet {
  .searchWrapper {
    padding: 12px;
  }

  .input {
    font-size: 14px;
  }

  .resultName {
    font-size: 14px;
  }
}
