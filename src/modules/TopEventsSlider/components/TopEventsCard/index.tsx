import React from 'react';
import styles from './index.module.scss';
import type { BetOddT, SportType } from '@/types/entities.ts';
import { SPORT_ICON_MAP } from '@/utils/constants.ts';

import ChevronRightIcon from '@/assets/icons/expand_more_24px.svg?react';
import { BetOdd } from '@/components/BetOdd';

export type TopEventCardProps = {
  id: number;
  date: string;
  league: string;
  teams: [string, string];
  logos: [string, string];
  odds: BetOddT[];
  sportType: SportType;
  activeOddId: number | null;
};

export const TopEventCard: React.FC<TopEventCardProps> = ({
  league,
  date,
  teams,
  logos,
  odds,
  sportType,
  activeOddId,
}) => {
  const imageSrc = SPORT_ICON_MAP[sportType];

  return (
    <div className={styles.card}>
      <div className={styles.leagueRow}>
        <div className={styles.leagueHeader}>
          <img src={imageSrc} alt={league} className={styles.sportIcon} />
          <span className={styles.league}>{league}</span>
        </div>
        <div className={styles.date}>{date}</div>
      </div>
      <div className={styles.teamsRow}>
        <div className={styles.team}>
          <img src={logos[0]} className={styles.logo} alt={teams[0]} />
          <span>{teams[0]}</span>
        </div>

        <span className={styles.vs}>VS</span>

        <div className={styles.team}>
          <img src={logos[1]} className={styles.logo} alt={teams[1]} />
          <span>{teams[1]}</span>
        </div>
      </div>

      <div className={styles.oddsRow}>
        {odds.map((odd) => (
          <BetOdd
            key={odd.id}
            value={odd.value}
            direction={odd.direction}
            isLocked={odd.isLocked}
            active={odd.id === activeOddId}
          />
        ))}
        <button className={styles.button} onClick={() => {}}>
          <ChevronRightIcon />
        </button>
      </div>
    </div>
  );
};
