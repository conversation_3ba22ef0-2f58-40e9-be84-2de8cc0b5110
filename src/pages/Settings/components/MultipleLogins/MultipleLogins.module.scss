@use '@/styles/media' as *;

.multipleLogins {
  .innerWrapper {
    background-color: var(--neutral-600);
    border-radius: 8px;
    padding: 0 15px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
    padding-top: 15px;

    @include mobile {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
  }

  .title {
    color: var(--white);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    margin: 0;
  }

  .terminateAllButton {
    background-color: var(--error);
    border-color: var(--error);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    height: 36px;
    border-radius: 6px;

    &:hover {
      background-color: var(--error-3);
      border-color: var(--error-3);
    }

    &:disabled {
      background-color: var(--neutral-400);
      border-color: var(--neutral-400);
      color: var(--neutral-300);
    }
  }

  .sessionsListHeading {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 20px 0 0 0;

    h3 {
      flex-basis: 50%;
      color: var(--neutral-200);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-normal);
      margin: 0;
    }
  }

  .ipAddressHeading {
    padding-left: 15px;
  }

  .sessionsList {
    padding-bottom: 20px;
  }

  .sessionItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 17px 0 4px;
    border-bottom: 1px solid var(--neutral-500);

    &:last-child {
      border-bottom: none;
    }

    @include mobile {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }
  }

  .sessionInfo {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    @include mobile {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }

  .deviceInfo {
    display: flex;
    align-items: flex-start;
    flex: 1 0 50%;
    flex-direction: column;
  }

  .deviceName {
    color: var(--neutral-50);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin: 0 0 4px 0;
  }

  .deviceStatus {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: var(--font-size-xs);
  }

  .statusActive {
    color: var(--success);
    font-weight: var(--font-weight-medium);
  }

  .statusInactive {
    color: var(--secondary-500);
    font-weight: var(--font-weight-medium);
  }

  .lastActive {
    color: var(--neutral-300);
    font-weight: var(--font-weight-normal);
  }

  .sessionDetails {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    flex-basis: 50%;
    padding-left: 15px;

    @include mobile {
      width: 100%;
    }
  }

  .ipAddress {
    color: var(--neutral-50);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    margin: 0;
  }

  .sessionActions {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 10px;

    @include mobile {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .terminateButton {
    background-color: transparent;
    border: 1px solid var(--error);
    color: var(--error);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    height: 32px;
    border-radius: 4px;
    padding: 0 12px;

    &:hover {
      background-color: var(--error);
      color: var(--white);
    }

    &:disabled {
      border-color: var(--neutral-400);
      color: var(--neutral-400);
      background-color: transparent;
    }
  }

  .emptyState {
    text-align: center;
    padding: 40px 20px;
    color: var(--neutral-300);
    font-size: var(--font-size-sm);
  }
}

.skeleton {
  .titleSkeleton {
    width: 200px;
    height: 36px;
  }

  .buttonSkeleton {
    width: 150px;
    height: 36px;
  }

  .deviceSkeleton {
    width: 100%;
    height: 45px;
  }

  .sessionItem {
    .sessionInfo {
      .deviceInfo {
        flex: 1 0 50%;
      }

      .sessionDetails {
        flex-basis: 50%;
        padding-left: 15px;
        display: flex;
        align-items: center;
      }
    }
  }
}
