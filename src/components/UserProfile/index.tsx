import { Button, Dropdown, Avatar, type MenuProps } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { type AppDispatch, type RootState } from '@/store';
import { logout } from '@/store/slices/authSlice';
import { USER_PROFILE_MENU_ITEMS } from '@/utils/constants';

import { IoChevronDownOutline, IoChevronUpOutline } from 'react-icons/io5';

import styles from './index.module.scss';

import AvatarIcon from '@/assets/icons/user-avatar.svg?react';
import { useState } from 'react';

export const UserProfile = () => {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  const handleOpenChange = (flag: boolean) => {
    setOpen(flag);
  };

  const handleLogout = () => {
    dispatch(logout());
    setOpen(false);
  };

  const menuItems: MenuProps['items'] = USER_PROFILE_MENU_ITEMS.map((item) => {
    if (item.type === 'divider') {
      return { type: 'divider' };
    }

    if (item.isAction && item.key === 'logout') {
      return {
        key: item.key,
        icon: <item.icon />,
        label: item.label,
        onClick: handleLogout,
      };
    }

    return {
      key: item.key,
      icon: <item.icon />,
      label: (
        <Link to={item.path!} onClick={() => setOpen(false)} className={styles.menuLink}>
          {item.label}
        </Link>
      ),
    };
  });

  if (!user) return null;

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['click']}
      onOpenChange={handleOpenChange}
      open={open}
    >
      <Button type="text" className={styles.userProfileButton}>
        <Avatar size={32} src={user.avatar} icon={<AvatarIcon />} style={{ flexShrink: 0 }} />
        <span className={styles.userName}>{user.name}</span>

        {open ? (
          <IoChevronUpOutline className={styles.chevronIcon} />
        ) : (
          <IoChevronDownOutline className={styles.chevronIcon} />
        )}
      </Button>
    </Dropdown>
  );
};
