@use '@/styles/media' as *;

.userProfileButton {
  height: auto;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--neutral-700);
  }

  &:focus {
    background-color: var(--neutral-700);
  }
}

.userName {
  color: var(--white);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);

  @include mobile {
    display: none;
  }
}

.chevronIcon {
  color: var(--neutral-300);
  font-size: 16px;
  transition: transform 0.2s ease;
}

.menuLink {
  color: inherit;
  text-decoration: none;
  display: block;
  width: 100%;

  &:hover {
    color: inherit;
    text-decoration: none;
  }

  &:focus {
    color: inherit;
    text-decoration: none;
  }
}
