@use '@/styles/media' as *;

.container {
  display: flex;
  flex-direction: column;


  & .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 42px;
    padding: 0 8px 0 16px;
    background: var(--neutral-500);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    transition:  0.3s ease;


    & .toggleButton {
      display: flex;
      justify-content: center;
      margin-left: 12px;
      background: transparent;
      cursor: pointer;
      width: 56px;
    }

    & .row {
      display: flex;
      align-items: center;
    }

    & .headerSportTypeImage {
      width: 32px;
      height: 32px;
    }

    & .leagueTitle {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--neutral-20);
      margin-right: 8px;
    }

    & .gameCount {
      width: 18px;
      height: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: rgba(129, 132, 124, 0.5);
      color: var(--white);
      font-size: var(--font-size-xs);
      line-height: 20px;
    }

    & .oddsLabelsContainer {
      display: flex;
      align-items: center;
      column-gap: 4px;

      @include mobile {
      display: none;
    }

      & .oddLabel {
        width: 86.67px;
        text-align: center;
        font-size: var(--font-size-sm);
        line-height: 22px;
        color: var(--gray);
        text-transform: capitalize;

        @include laptop {
          width: 56px;
        }

        &:nth-child(2) {
          text-transform: lowercase;
        }

        &:nth-child(3) {
          margin-right: 16px;
        }
      }
    }
  }

  & .headerNotExpanded {
    border-radius: 8px;
  }

  & .gamesContainer {
    display: flex;
    flex-direction: column;
    row-gap: 1px;

    @include mobile {
      row-gap: 0;
    }
  }

  .toggleButton {
    transition: transform 0.3s ease;
  }

  .iconOpened {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
  }

  .iconClosed {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
  }

  & .footer {
    display: flex;
    align-items: center;
    height: 42px;
    background: var(--neutral-500);
    padding: 0 16px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    & .loadMoreButton {
      background: transparent;
      font-weight: var(--font-weight-bold);
      font-size: var(--font-size-sm);
      line-height: 20px;
      color: var(--neutral-50);
      cursor: pointer;
      transition: 0.3s ease-in-out;

      &:hover {
        color: var(--primary-500);
      }
    }
  }
}