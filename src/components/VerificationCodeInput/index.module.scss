@use '@/styles/media' as *;

.outerContainer {
  p {
    font-size: var(--font-size-xs);
    color: var(--neutral-300);
    font-weight: var(--font-weight-semibold);
    text-align: left;
    margin-bottom: 10px;
  }
}

.container {
  display: flex;
  gap: 12px;
  justify-content: flex-start;

  @include mobile {
    gap: 8px;
  }
}

.input {
  width: 52px;
  height: 64px;
  text-align: center;
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-semibold);
  border-radius: 8px;
  border: 1px solid var(--neutral-500);
  background: var(--neutral-800);
  color: var(--white);
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--neutral-400);
  }

  &:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(var(--primary-500-rgb), 0.1);
  }

  &.error {
    border-color: var(--error) !important;

    &:focus {
      border-color: var(--error) !important;
    }

    &:hover {
      border-color: var(--error) !important;
    }
  }
}
