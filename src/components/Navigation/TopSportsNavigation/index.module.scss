.container {
  padding: 24px 12px;

  & .arrowIconContainer {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: 0.3s ease-in-out;
    border-radius: 4px;

    & .arrowIcon {
      transition: 0.3s ease-in-out;
      color: var(--neutral-400);
      font-size: 16px;
    }

    & .arrowUp {
      transform: rotate(180deg)
    }

    & .arrowDown {
      transform: rotate(0deg);
    }

    &:hover {
      background-color: var(--neutral-500);
    }

    &:hover .arrowIcon {
      color: var(--neutral-200);
    }

    &:active {
      width: 28px;
      height: 28px;
    }

    &.open {
      .arrowIcon {
        color: var(--white);
      }
    }
  }

  & .navTitle {
    font-weight: var(--font-weight-semi-bold);
    font-size: var(--font-size-sm);
    line-height: 20px;
    color: var(--white);
    opacity: 0.5;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  & .sidebarTopSportsMenu {
    margin-top: 8px;
    width: 100%;
    background-color: transparent;

    :global(.ant-menu-submenu) {
      margin-bottom: 4px;
    }

    :global(.ant-menu-submenu-title) {
      margin: 0;
      padding: 8px !important; // override ant inline default left paddings
      min-height: 48px;
      width: 100%;
      transition: 0.3s ease-in-out;
      overflow: visible;

      &::before {
        content: '';
        position: absolute;
        right: -12px;
        height: 100%;
        width: 3px;
        background-color: var(--primary-500);
        transform: scaleY(0);
        transform-origin: top;
        transition: transform 0.3s ease-in-out;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }
    }

    :global(.ant-menu-submenu-selected .ant-menu-submenu-title::before) {
      transform: scaleY(1);
    }

    :global(.ant-menu-submenu-open > .ant-menu-submenu-title) {
      background: var(--neutral-500);
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    :global(.ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-title-content) {
      color: var(--neutral-50)
    }

    :global(.ant-menu-submenu-open > .ant-menu-submenu-title:hover) {
      background: var(--neutral-500);
    }

    :global(.ant-menu-submenu-open > .ant-menu-submenu-title:hover > div) {
      background: var(--neutral-600);
    }

    :global(.ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-title-content) {}

    :global(.ant-menu-submenu-title .ant-menu-item-icon) {
      width: 32px;
      height: 32px;
      transition: 0.2s ease-in-out;
    }

    :global(.ant-menu-submenu-title:hover .ant-menu-title-content) {
      color: var(--neutral-50);
    }

    :global(.ant-menu-submenu-title:hover ) {
      background: var(--neutral-600)
    }

    :global(.ant-menu-submenu-title:active .ant-menu-title-content) {
      color: var(--neutral-200);
      font-size: var(--font-size-sm);
    }

    :global(.ant-menu-submenu-title:active .ant-menu-item-icon) {
      transform: scale(0.9);
    }

    :global(.ant-menu-submenu-title .ant-menu-title-content) {
      margin-left: 6px;
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-sm-md);
      line-height: 28px;
      color: var(--neutral-200);
      transition: 0.2s ease-in-out;
    }

    :global(.ant-menu-submenu-selected .ant-menu-submenu-title) {
      background: linear-gradient(90deg, var(--primary-750) 0%, rgba(90, 103, 16, 0) 100%);

      &:hover,&:active {
        background: linear-gradient(90deg, var(--primary-750) 0%, rgba(90, 103, 16, 0) 100%);
      }
    }

    :global(.ant-menu-submenu-selected .ant-menu-submenu-title svg) {
      color: var(--neutral-400);
    }

    :global(.ant-menu-sub) {
      margin-top: 4px;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      padding: 12px 0;
      background-color: var(--neutral-700)
    }

    :global(.ant-menu-item) {
      width: 100%;
      padding: 0 12px 0 16px !important; // override ant inline default left paddings
      margin: 0 0 4px;
      height: 44px;
      transition: 0.3s ease-in-out;
      border-radius: 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :global(.ant-menu-item:last-child .ant-menu-title-content) {
      font-weight: var(--font-weight-bold);
      font-size: var(--font-size-sm);
      line-height: 20px;
      color: var(--neutral-50);
    }

    // hover

    :global(.ant-menu-item:hover) {
      background: var(--neutral-600);
    }

    :global(.ant-menu-item:hover .ant-menu-title-content) {
      color: var(--neutral-200);
    }
    :global(.ant-menu-item:hover svg.ant-menu-item-icon) {
      fill: var(--neutral-200);
    }

    // active

    :global(.ant-menu-item:active .ant-menu-title-content) {
      color: var(--neutral-200);
    }

    :global(.ant-menu-item:active svg.ant-menu-item-icon) {
      fill: var(--neutral-200);
    }

    :global(.ant-menu-item .ant-menu-item-icon) {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      transition: 0.3s ease-in-out;
    }

    :global(.ant-menu-item svg) {
      fill: var(--neutral-300)
    }

    :global(.ant-menu-item .ant-menu-title-content) {
      margin-left: 0;
      font-weight: var(--font-weight-semibold);
      font-size: var(--font-size-sm-md);
      line-height: 28px;
      color: var(--neutral-200);
    }

    :global(.ant-menu-item-selected) {
      background: var(--neutral-600);
    }

    :global(.ant-menu-item-selected .ant-menu-title-content) {
      color: var(--neutral-50);
    }

    :global(.ant-menu-item-selected svg.ant-menu-item-icon) {
      fill: var(--neutral-50);
    }
  }
}