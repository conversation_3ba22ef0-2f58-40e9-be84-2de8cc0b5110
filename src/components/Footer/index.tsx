import styles from './index.module.scss';

import AppLogo from '@/assets/images/Logo.png';
import Twitter from '@/assets/images/twitter.png';
import Facebook from '@/assets/images/facebook.png';
import Instagram from '@/assets/images/instagram.png';

import AskGamblers from '@/assets/images/ask_gamblers.png';
import GamblingTherapy from '@/assets/images/gambling_therapy.png';
import EighteenPlus from '@/assets/images/18plus.png';
import GamblersAnonymous from '@/assets/images/gamblers_anonymous.png';
import { OddsTypeSelector } from '@/components';
import { MobileFooterMenu } from './MobileFooterMenu';
import { FOOTER_COLUMNS } from '@/utils/constants';

export const Footer = () => {
  return (
    <footer className={styles.footer}>
      <div className={styles.topBar}>
        <img src={AppLogo} alt="logo" className={styles.logo} />
        <div className={styles.socials}>
          <button>
            <img src={Twitter} alt="twitter" />
          </button>
          <button>
            <img src={Facebook} alt="facebook" />
          </button>
          <button>
            <img src={Instagram} alt="instagram" />
          </button>
        </div>
      </div>

      {/* link columns */}
      <div className={styles.columns}>
        {FOOTER_COLUMNS.map((col) => (
          <nav key={col.title} className={styles.col}>
            <h4 className={styles.colTitle}>{col.title}</h4>
            <ul className={styles.colList}>
              {col.items.map((item) => (
                <li key={item} className={styles.colItem}>
                  <a href="#" aria-label={item}>
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </nav>
        ))}
      </div>

      <MobileFooterMenu
        columns={FOOTER_COLUMNS}
        onItemClick={(section, item) => console.log('click', section, item)}
        defaultActiveKey="Main"
      />

      {/* legal text */}
      <div className={styles.legal}>
        <p className={styles.legalBlock}>
          Copyright &copy; 2024 LisaParyaj is owned and operated by LisaParyaj Ltd., a company
          incorporated under the laws of Curaçao with registration number 152125, and registered at
          Scharlooweg 39, Willemstad, Curaçao. LisaParyaj Ltd. operates under E-gaming License No.
          8048/JAZ2020-13, issued by Antillephone N.V. and authorized by the Government of Curaçao.
        </p>
        <p className={styles.legalBlock}>
          LisaParyaj Ltd., a company incorporated under the laws of Cyprus with registration number
          HE 419102, serves as a facilitating entity within the LisaParyaj group. Its registered
          address is Leandrou 12A, 3086 Limassol, Cyprus. LisaParyaj holds E-gaming licenses from
          both Curaçao (8048/JAZ2020-13) and Estonia (HKL000255). All Paysafe payments are processed
          by LisaParyaj Ltd. Player
        </p>
      </div>

      {/* badges + controls */}
      <div className={styles.bottomBar}>
        <div className={styles.badges}>
          <img src={AskGamblers} alt="ask Gamblers" />
          <img src={GamblingTherapy} alt="gambling therapy" />
          <img src={EighteenPlus} alt="eighteen plus" />
          <img src={GamblersAnonymous} alt="gamblers anonymous" />
        </div>

        <div className={styles.controls}>
          <OddsTypeSelector />
        </div>
      </div>
    </footer>
  );
};
