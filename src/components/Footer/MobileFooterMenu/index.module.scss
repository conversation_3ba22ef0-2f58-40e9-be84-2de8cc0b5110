@use '@/styles/media.scss' as *;

.wrapper {
  width: 100%;

  @include tablet {
    display: none;
  }

  @include desktop {
    display: none;
  }

  @include laptop {
    display: none;
  }
}

:global(.ant-collapse) {
  background: transparent;
  border: 0;
}
:global(.ant-collapse > .ant-collapse-item) {
  margin-bottom: 16px;
  border: 0;
}
:global(.ant-collapse .ant-collapse-header) {
  padding: 12px 14px;
  background: var(--neutral-700);
  border-radius: 9px;
}
:global(.ant-collapse-item-active .ant-collapse-header) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

:global(.ant-collapse-content) {
  background: var(--neutral-900);
  border-top: 0;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
:global(.ant-collapse-content-box) {
  padding: 8px 10px 12px;
}

.headerInner {
  display: flex;
  align-items: center;
  gap: 8px;
}
.headerText {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm-md);
  line-height: 28px;
  color: var(--white);
}
.chevron {
  font-size: var(--font-size-xs);
  opacity: 0.85;
  color: var(--neutral-200);
}

.list {
  list-style: none;
  margin: 0;
  padding: 4px 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.listItem {
  min-height: 36px;
  display: flex;
  align-items: center;
}
.link {
  display: block;
  width: 100%;
  padding: 8px 6px;
  border-radius: 8px;
  color: var(--neutral-200);
  text-decoration: none;
  transition: background 0.15s ease;

  &:active {
    transform: translateY(0.5px);
  }
  &:hover {
    background: rgba(255, 255, 255, 0.04);
    color: #eef7e7;
  }
}
