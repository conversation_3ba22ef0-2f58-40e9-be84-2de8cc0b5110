import { Form, Input } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useCallback } from 'react';
import { type RootState, type AppDispatch } from '@/store';
import { useLoginMutation } from '@/api/authApi';
import { openRegisterModal } from '@/store/slices/modalSlice';
import { setUser } from '@/store/slices/authSlice';
import UserWithKeyIcon from '@/assets/icons/user-with-key.svg?react';
import { useFormValidation } from '@/hooks/useFormValidation';
import styles from './index.module.scss';
import { toast } from 'sonner';
import { AppButton } from '@/components';

interface LoginFormData {
  email: string;
  password: string;
}

const loginValidationRules = {
  email: [
    { required: true, message: 'Email should be stated' },
    { type: 'email' as const, message: 'Email address not found' },
  ],
  password: [{ required: true, message: 'Password should be stated' }],
};

export const LoginForm = ({
  onCancel,
  setLoginFormActive,
}: {
  onCancel: () => void;
  setLoginFormActive: (active: boolean) => void;
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [form] = Form.useForm();
  const { loginModalOpen } = useSelector((state: RootState) => state.modal);
  const [login, { isLoading: isLoginLoading }] = useLoginMutation();

  const { isStepValid, checkCurrentStepValidity, setIsStepValid } = useFormValidation({
    form,
    fields: ['email', 'password'],
    enabled: loginModalOpen,
  });

  const resetFormAndState = useCallback(() => {
    setIsStepValid(false);
    form?.resetFields();
  }, [setIsStepValid, form]);

  const handleCancel = useCallback(() => {
    resetFormAndState();
    onCancel();
  }, [resetFormAndState, onCancel]);

  const handleSubmit = async (values: LoginFormData) => {
    try {
      const response = await login(values).unwrap();
      dispatch(setUser(response.user));
      toast.success('Successfully logged in!');
      handleCancel();
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Login failed';
      toast.error(errorMessage);
    }
  };

  const switchToRegister = () => {
    resetFormAndState();
    dispatch(openRegisterModal());
  };

  return (
    <>
      <div className={styles.loginWrapper} key="login-form">
        <div className={styles.header}>
          <UserWithKeyIcon />
          <p className={styles.title}>Welcome back</p>
          <p className={styles.text}>Login to your account</p>
        </div>

        <Form
          form={form}
          name="loginForm"
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
          preserve={false}
          onValuesChange={checkCurrentStepValidity}
          onFieldsChange={checkCurrentStepValidity}
        >
          <Form.Item
            name="email"
            label="Email"
            rules={loginValidationRules.email}
            className={styles.email}
          >
            <Input placeholder="Enter your email address" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={loginValidationRules.password}
            className={styles.password}
          >
            <Input.Password placeholder="Enter your password" />
          </Form.Item>

          <div className={styles.actions}>
            <Form.Item className={styles.loginButton}>
              <AppButton
                variant="primary"
                htmlType="submit"
                loading={isLoginLoading}
                disabled={!isStepValid}
                block
              >
                {isLoginLoading ? 'Logging in...' : 'Login'}
              </AppButton>
            </Form.Item>

            <Form.Item className={styles.forgotPassword}>
              <AppButton
                variant="link"
                className={styles.forgotPasswordLink}
                onClick={() => setLoginFormActive(false)}
                disabled={isLoginLoading}
              >
                Forgot password?
              </AppButton>
            </Form.Item>
          </div>
        </Form>
      </div>

      <div className={styles.footer}>
        <span className={styles.footerText}>Don't have an account? </span>
        <AppButton variant="link" onClick={switchToRegister}>
          Register now
        </AppButton>
      </div>
    </>
  );
};
