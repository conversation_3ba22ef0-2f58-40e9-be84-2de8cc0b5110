import { Form, Input } from 'antd';
import { useSelector } from 'react-redux';
import { type RootState } from '@/store';
import { useForgotPasswordMutation } from '@/api/authApi';
import ForgotPasswordIcon from '@/assets/icons/chain.svg?react';
import LetterIcon from '@/assets/icons/letter.svg?react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { validationRules } from '@/utils/validationRules';
import styles from './index.module.scss';
import { LuArrowLeft } from 'react-icons/lu';
import { useState } from 'react';
import { AppButton } from '@/components';
import { toast } from 'sonner';

interface ForgotPasswordFormData {
  email: string;
}

export const ForgotPasswordForm = ({
  setLoginFormActive,
}: {
  setLoginFormActive: (active: boolean) => void;
}) => {
  const [forgotPasswordFormActive, setForgotPasswordFormActive] = useState(true);
  const [form] = Form.useForm();
  const { loginModalOpen } = useSelector((state: RootState) => state.modal);
  const [forgotPassword, { isLoading: isForgotPasswordLoading }] = useForgotPasswordMutation();

  const { isStepValid, checkCurrentStepValidity } = useFormValidation({
    form,
    fields: ['email'],
    enabled: loginModalOpen,
  });

  const handleSubmit = async (data: ForgotPasswordFormData) => {
    try {
      await forgotPassword(data).unwrap();
      toast.success('Password reset link sent to your email');
      setForgotPasswordFormActive(false);
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to send reset email';
      toast.error(errorMessage);
    }
  };

  const renderBackBtn = (cb: (active: boolean) => void) => {
    return (
      <AppButton
        variant="default"
        key="back"
        onClick={() => cb(true)}
        disabled={isForgotPasswordLoading}
        className={styles.backButton}
      >
        <LuArrowLeft size={20} /> Back
      </AppButton>
    );
  };

  return (
    <div key="login-form">
      <div className={styles.forgotPasswordWrapper}>
        <div className={styles.header}>
          {forgotPasswordFormActive ? <ForgotPasswordIcon /> : <LetterIcon />}
          <p className={styles.text}>
            {forgotPasswordFormActive ? 'Reset password' : 'Check your mailbox'}
          </p>
          <p className={styles.title}>
            {forgotPasswordFormActive
              ? 'Please enter your email address. We will send you an email to reset your password.'
              : "We've sent you an email to reset your password."}
          </p>
        </div>

        {forgotPasswordFormActive ? (
          <Form
            form={form}
            name="loginForm"
            layout="vertical"
            onFinish={handleSubmit}
            autoComplete="off"
            preserve={false}
            onValuesChange={checkCurrentStepValidity}
            onFieldsChange={checkCurrentStepValidity}
          >
            <Form.Item
              name="email"
              label="Email"
              rules={validationRules.email}
              className={styles.email}
            >
              <Input placeholder="Enter your email address" />
            </Form.Item>

            <div className={styles.actions}>
              <Form.Item className={styles.forgotPassword}>
                {renderBackBtn(setLoginFormActive)}
              </Form.Item>
              <Form.Item className={styles.loginButton}>
                <AppButton
                  variant="primary"
                  htmlType="submit"
                  loading={isForgotPasswordLoading}
                  disabled={!isStepValid}
                  block
                >
                  {isForgotPasswordLoading ? 'Submitting...' : 'Submit'}
                </AppButton>
              </Form.Item>
            </div>
          </Form>
        ) : (
          renderBackBtn(setForgotPasswordFormActive)
        )}
      </div>
    </div>
  );
};
