import { Modal, Form, Input, Button } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useCallback, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { type RootState, type AppDispatch } from '@/store';
import { useResetPasswordMutation } from '@/api/authApi';
import { closeModals } from '@/store/slices/modalSlice';
import { CloseOutlined } from '@ant-design/icons';
import ChainSuccessIcon from '@/assets/icons/chain-success.svg?react';
import { PasswordValidator } from '@/components';
import { useFormValidation } from '@/hooks/useFormValidation';
import { validationRules } from '@/utils/validationRules';
import styles from './index.module.scss';
import { toast } from 'sonner';

interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

export const ResetPasswordModal = () => {
  const [isSuccess, setIsSuccess] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const [form] = Form.useForm();
  const [searchParams] = useSearchParams();

  const resetPasswordToken = searchParams.get('token');

  const { resetPasswordModalOpen } = useSelector((state: RootState) => state.modal);
  const [resetPassword, { isLoading: isResetPasswordLoading }] = useResetPasswordMutation();

  const { isStepValid, checkCurrentStepValidity } = useFormValidation({
    form,
    fields: ['password', 'confirmPassword'],
    enabled: resetPasswordModalOpen,
  });

  const clearUrlParams = () => {
    const url = new URL(window.location.href);
    url.searchParams.delete('token');
    url.searchParams.delete('action');
    window.history.replaceState({}, '', url.toString());
  };

  const handleCancel = useCallback(() => {
    dispatch(closeModals());
    form.resetFields();
    setIsSuccess(false);
    clearUrlParams();
  }, [dispatch, form]);

  const handleSubmit = async (values: ResetPasswordFormData) => {
    if (!resetPasswordToken) {
      toast.error('Invalid reset link. Please request a new password reset.');
      return;
    }

    try {
      await resetPassword({
        token: resetPasswordToken,
        password: values.password,
        confirmPassword: values.confirmPassword,
      }).unwrap();

      toast.success('Password successfully reset!');
      setIsSuccess(true);
      form.resetFields();
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to reset password';
      toast.error(errorMessage);
    }
  };

  const handleSuccessClose = useCallback(() => {
    setIsSuccess(false);
    dispatch(closeModals());
    clearUrlParams();
  }, [dispatch]);

  return (
    <Modal
      open={resetPasswordModalOpen}
      onCancel={handleCancel}
      footer={null}
      centered
      transitionName=""
      maskTransitionName=""
      className={styles.resetPassword}
      closeIcon={<CloseOutlined />}
      maskClosable={false}
    >
      <div className={styles.resetPasswordWrapper}>
        <div className={styles.header}>
          <ChainSuccessIcon />
          <p className={styles.title}>
            {isSuccess ? 'Password Changed Successfully!' : 'Set new password'}
          </p>
        </div>

        {!isSuccess ? (
          <Form
            form={form}
            name="resetPasswordForm"
            layout="vertical"
            onFinish={handleSubmit}
            autoComplete="off"
            preserve={false}
            onValuesChange={checkCurrentStepValidity}
            onFieldsChange={checkCurrentStepValidity}
          >
            <PasswordValidator
              name="password"
              placeholder="Enter new password"
              label="New Password"
              className={styles.password}
            />

            <Form.Item
              name="confirmPassword"
              label="Repeat Password"
              dependencies={['password']}
              rules={validationRules.confirmPassword(form.getFieldValue)}
              className={styles.confirmPassword}
            >
              <Input.Password placeholder="Repeat password" />
            </Form.Item>

            <div className={styles.actions}>
              <Button
                type="primary"
                htmlType="submit"
                loading={isResetPasswordLoading}
                disabled={!isStepValid}
                className={styles.submitButton}
              >
                Save password
              </Button>
            </div>
          </Form>
        ) : (
          <div className={styles.successContent}>
            <p>Your password has been updated. You can now use your new password to log in.</p>

            <Button type="primary" onClick={handleSuccessClose}>
              Continue
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};
