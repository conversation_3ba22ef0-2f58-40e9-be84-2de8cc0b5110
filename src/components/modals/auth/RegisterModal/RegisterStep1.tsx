import { Form, Input, DatePicker, Row, Col } from 'antd';
import { validationRules } from '@/utils/validationRules';
import styles from './index.module.scss';

export const RegisterStep1 = () => (
  <>
    <Form.Item name="username" rules={validationRules.username}>
      <Input placeholder="Username" />
    </Form.Item>

    <Row gutter={8}>
      <Col span={12}>
        <Form.Item name="name" rules={validationRules.name}>
          <Input placeholder="Name" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item name="surname" rules={validationRules.surname}>
          <Input placeholder="Surname" />
        </Form.Item>
      </Col>
    </Row>

    <Form.Item
      name="birthday"
      className={styles.birthday}
      rules={[
        ...validationRules.birthday,
        {
          validator: (_: unknown, value: unknown) => {
            if (!value) {
              return Promise.resolve();
            }
            // Additional format validation could be added here if needed
            // The DatePicker component already enforces DD.MM.YYYY format
            return Promise.resolve();
          },
        },
      ]}
      extra="Format: dd.mm.yyyy"
    >
      <DatePicker
        placeholder="Birthday"
        format="DD.MM.YYYY"
        style={{ width: '100%' }}
        inputReadOnly
      />
    </Form.Item>
  </>
);
