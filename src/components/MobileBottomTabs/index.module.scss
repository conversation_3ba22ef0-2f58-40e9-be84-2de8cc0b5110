@use '@/styles/media' as *;

.footerNavMenu {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 100;
  height: 64px;
  background-color: var(--neutral-600);
  border-top: 1px solid var(--neutral-500);

  @include desktop {
    display: none;
  }

  @include laptop {
    display: none;
  }

  :global(.ant-menu-item) {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: calc(100% / 5);
    padding: 0;
    transition: 0.3s ease-in-out;

    border-top: 2px solid transparent;

    @include tablet {
      min-width: 154px;
    }

    @include mobile {
      min-width: 75px;
    }
  }

  :global(.ant-menu-item-icon) {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
    fill: var(--neutral-300);
  }

  :global(.ant-menu-item-selected) {
    background: linear-gradient(180deg, var(--primary-750) 0%, var(--primary-750-transparent) 100%);
    border-top-color: var(--primary-500);

    & > span {
      color: var(--primary-500);
    }

    & > svg {
      fill: var(--primary-500);
    }
  }

  :global(.ant-menu-title-content) {
    margin: 0;
    line-height: 20px;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xs);
    color: var(--neutral-200);

    @include mobile {
      font-size: var(--font-size-xss);
    }
  }
}
