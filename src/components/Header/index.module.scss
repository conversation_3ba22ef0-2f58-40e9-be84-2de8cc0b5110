@use '@/styles/media' as *;

.container {
  height: 68px;
  width: 100%;
  border-bottom: 1px solid var(--light-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: var(--neutral-700);
  backdrop-filter: blur(8px);

  position: sticky;
  top: 0;
  z-index: 100;

  :global(.ant-menu-root) {
    background: transparent;
    height: 100%;
  }

  @include tablet {
    height: 60px;
    padding: 0 24px;
    border-bottom: none;
  }

  @include mobile {
    border-bottom: none;
    padding: 0 12px;
  }

  & .appLogo {
    width: 140px;
    height: 22px;
  }
}

.topNavMenu {
  :global(.ant-menu-item) {
    color: var(--neutral-300);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    padding: 0 8px;
    margin-right: 12px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      height: 2px;
      width: 100%;
      background-color: var(--primary-500);
      transform: scaleX(0);
      transform-origin: bottom left;
      transition: transform 0.3s ease-in-out;
    }

    & :last-child {
      margin-right: 0;
    }

    &:hover svg {
      fill: var(--neutral-50);
    }

    &:hover {
      color: var(--neutral-50);
    }
  }

  :global(.ant-menu-item-selected::before) {
    transform: scaleX(1);
  }

  :global(.ant-menu-item-icon) {
    fill: var(--neutral-300);
    transition: fill 0.2s ease-in-out;
  }

  :global(.ant-menu-item-selected:hover),
  :global(.ant-menu-item-selected) {
    background: transparent;
    color: var(--primary-500);

    & > svg {
      fill: var(--primary-500) !important;
    }
  }
}

.authSection {
  display: flex;
  align-items: center;

  & .row {
    display: flex;
    align-items: center;
    gap: 32px;
  }

  & .leftSection {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.authButtons {
  margin-left: 32px;
  display: flex;
  align-items: center;
  gap: 8px;
}
