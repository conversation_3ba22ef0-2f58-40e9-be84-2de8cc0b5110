@use '@/styles/media' as *;

.sidebar {
  height: 100vh;
  min-height: 100vh;
  width: 280px;
  position: fixed !important;
  z-index: 102;

  @include tablet {
    display: none;
  }

  @include mobile {
    display: none;
  }
}

.leftSidebar {
  width: 280px;
  background-color: var(--neutral-800);
  flex-shrink: 0;
  padding: 24px 0;
  min-height: 100vh;
  border-right: 1px solid var(--neutral-600);

  @include tablet {
    display: none;
  }

  @include mobile {
    display: none;
  }

  & .appLogo {
    padding-left: 16px;
  }

  & .sectionSeparator {
    width: 100%;
    height: 2px;
    background-color: var(--white-20-transparent);
    box-shadow: 0px -2px 0px 0px var(--black);
  }
}
