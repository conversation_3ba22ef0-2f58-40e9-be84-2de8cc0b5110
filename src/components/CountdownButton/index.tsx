import { useState, useEffect } from 'react';

import styles from './index.module.scss';
import { AppButton } from '@/components';

interface CountdownButtonProps {
  initialTime?: number;
  onResend: () => void | Promise<void>;
  loading?: boolean;
  disabled?: boolean;
  children?: React.ReactNode;
  loadingLabel?: string;
  className?: string;
  type?: 'link';
  size?: 'large' | 'middle' | 'small';
  autoStart?: boolean;
}

export const CountdownButton = ({
  initialTime = 60,
  onResend,
  loading = false,
  disabled = false,
  children = 'Resend code',
  loadingLabel,
  className,
  type = 'link',
  size = 'middle',
  autoStart = false,
}: CountdownButtonProps) => {
  const [timeLeft, setTimeLeft] = useState(0);
  const [isActive, setIsActive] = useState(false);

  const startTimer = () => {
    setTimeLeft(initialTime);
    setIsActive(true);
  };

  useEffect(() => {
    if (autoStart) {
      setTimeLeft(initialTime);
      setIsActive(true);
    }
  }, [autoStart, initialTime]);

  const handleResend = async () => {
    await onResend();
    startTimer();
  };

  useEffect(() => {
    let interval: number | null = null;

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((time) => {
          if (time <= 1) {
            setIsActive(false);
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isActive, timeLeft]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isDisabled = disabled || loading || isActive;

  return (
    <AppButton
      variant={type}
      onClick={handleResend}
      loading={loading}
      disabled={isDisabled}
      className={`${styles.resendButton} ${className || ''}`}
      size={size}
    >
      {loading ? (
        loadingLabel || children
      ) : isActive ? (
        <span className={styles.timerText}>
          {children} ({formatTime(timeLeft)})
        </span>
      ) : (
        children
      )}
    </AppButton>
  );
};
