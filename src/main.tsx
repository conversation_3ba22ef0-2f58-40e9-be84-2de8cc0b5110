import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { StyleProvider } from '@ant-design/cssinjs';
import { ConfigProvider } from 'antd';
import { ErrorBoundary } from 'react-error-boundary';

import 'antd/dist/reset.css';
import './styles/theme-default.css';
import './index.scss';

import { Provider } from 'react-redux';
import { persistor, store } from '@/store';
import App from './App.tsx';
import { PersistGate } from 'redux-persist/integration/react';
import { ErrorFallback } from '@/components';
import { Toaster } from 'sonner';
import { ResponsiveProvider } from '@/contexts/ResponsiveContext';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <StyleProvider layer>
        <ConfigProvider>
          <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
              <ResponsiveProvider>
                <App />
                <Toaster />
              </ResponsiveProvider>
            </PersistGate>
          </Provider>
        </ConfigProvider>
      </StyleProvider>
    </ErrorBoundary>
  </StrictMode>,
);
