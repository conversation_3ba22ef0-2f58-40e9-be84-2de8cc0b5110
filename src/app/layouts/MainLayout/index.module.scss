@use '@/styles/media' as *;

.container {
  display: flex;
  position: relative;

  & .mainWrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0; // prevent content overflow

    @include desktop {
      padding-left: 280px;
    }

    @include laptop {
      padding-left: 280px;
    }
  }

  & .contentWrapper {
    display: flex;
    width: 100%;
    max-width: 100%;
  }

  & .mainContent {
    flex: 1;
    min-width: 0;
    padding-top: 24px;
    padding-bottom: 24px;

    @include mobile {
      padding-top: 12px;
    }
  }
}
