import { type FC } from 'react';
import { Outlet } from 'react-router-dom';

import styles from './index.module.scss';
import { LeftSidebar } from '@/components/sidebars/LeftSidebar';
import { RightSidebar } from '@/components/sidebars/RightSidebar';
import { Header } from '@/components/Header';
import { MobileBottomTabs } from '@/components/MobileBottomTabs';
import { Footer } from '@/components/Footer';

export const MainLayout: FC = () => {
  return (
    <div className={styles.container}>
      <LeftSidebar />
      <div className={styles.mainWrapper}>
        <Header />
        <div className={styles.contentWrapper}>
          <div className={styles.mainContent}>
            <Outlet />
          </div>

          <RightSidebar />
        </div>
        <Footer />
      </div>

      <MobileBottomTabs />
    </div>
  );
};
