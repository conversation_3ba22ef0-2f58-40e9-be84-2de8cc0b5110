'use client';

import type { ReactNode } from 'react';
import { createContext, useEffect, useState, useMemo } from 'react';
import debounce from 'lodash/debounce';
import {
  MOBILE_WIDTH_BREAKPOINT,
  LARGE_WIDTH_BREAKPOINT,
  LAPTOP_WIDTH_BREAKPOINT,
} from '@/utils/constants';

interface ResponsiveContextType {
  isMobile: boolean;
  isTablet: boolean;
  isLaptop: boolean;
  isDesktop: boolean;
  width: number;
}

const ResponsiveContext = createContext<ResponsiveContextType>({
  isMobile: false,
  isTablet: false,
  isLaptop: false,
  isDesktop: true,
  width: LAPTOP_WIDTH_BREAKPOINT,
});

const ResponsiveProvider = ({ children }: { children: ReactNode }) => {
  const [width, setWidth] = useState<number>(() =>
    typeof window === 'object' ? window.innerWidth : LAPTOP_WIDTH_BREAKPOINT,
  );

  useEffect(() => {
    const debouncedHandleResize = debounce(() => {
      setWidth(window.innerWidth);
    }, 100);

    setWidth(window.innerWidth);

    window.addEventListener('resize', debouncedHandleResize);

    return () => {
      debouncedHandleResize.cancel(); // Отменяем pending debounce
      window.removeEventListener('resize', debouncedHandleResize);
    };
  }, []);

  const value = useMemo(
    () => ({
      isMobile: width <= MOBILE_WIDTH_BREAKPOINT,
      isTablet: width > MOBILE_WIDTH_BREAKPOINT && width <= LARGE_WIDTH_BREAKPOINT,
      isLaptop: width > LARGE_WIDTH_BREAKPOINT && width <= LAPTOP_WIDTH_BREAKPOINT,
      isDesktop: width > LAPTOP_WIDTH_BREAKPOINT,
      width,
    }),
    [width],
  );

  return <ResponsiveContext.Provider value={value}>{children}</ResponsiveContext.Provider>;
};

export { ResponsiveContext, ResponsiveProvider };
